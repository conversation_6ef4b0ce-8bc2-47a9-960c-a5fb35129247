import axios, { AxiosInstance, AxiosResponse } from 'axios';

const API_BASE_URL = 'http://localhost:8008/api/v1';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (credentials: { username: string; password: string }) => {
    const response = await api.post('/login', credentials);
    return response.data;
  },

  register: async (userData: {
    username: string;
    email: string;
    password: string;
    name: string;
  }) => {
    const response = await api.post('/register', userData);
    return response.data;
  },

  logout: async () => {
    const response = await api.post('/logout');
    return response.data;
  },

  getMe: async () => {
    const response = await api.get('/me');
    return response.data;
  },
};

// Transactions API
export const transactionsAPI = {
  getAll: async (filters?: any) => {
    const response = await api.get('/transactions', { params: filters });
    return response.data;
  },

  getById: async (id: string) => {
    const response = await api.get(`/transactions/${id}`);
    return response.data;
  },

  create: async (transaction: any) => {
    const response = await api.post('/transactions', transaction);
    return response.data;
  },

  update: async (id: string, transaction: any) => {
    const response = await api.put(`/transactions/${id}`, transaction);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/transactions/${id}`);
    return response.data;
  },
};

// Bank Statements API
export const bankStatementsAPI = {
  upload: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/bank-statements/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  import: async (entries: any[]) => {
    const response = await api.post('/bank-statements/import', entries);
    return response.data;
  },
};

// Accounts API
export const accountsAPI = {
  getAll: async () => {
    const response = await api.get('/accounts');
    return response.data;
  },

  getById: async (id: string) => {
    const response = await api.get(`/accounts/${id}`);
    return response.data;
  },

  create: async (account: any) => {
    const response = await api.post('/accounts', account);
    return response.data;
  },

  update: async (id: string, account: any) => {
    const response = await api.put(`/accounts/${id}`, account);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/accounts/${id}`);
    return response.data;
  },
};

// Categories API
export const categoriesAPI = {
  getAll: async () => {
    const response = await api.get('/categories');
    return response.data;
  },

  getById: async (id: string) => {
    const response = await api.get(`/categories/${id}`);
    return response.data;
  },

  create: async (category: any) => {
    const response = await api.post('/categories', category);
    return response.data;
  },

  update: async (id: string, category: any) => {
    const response = await api.put(`/categories/${id}`, category);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/categories/${id}`);
    return response.data;
  },
};

// Reports API
export const reportsAPI = {
  getSummary: async (startDate?: string, endDate?: string) => {
    const params: any = {};
    if (startDate) params.start_date = startDate;
    if (endDate) params.end_date = endDate;
    const response = await api.get('/reports/summary', { params });
    return response.data;
  },

  getCategoryBreakdown: async () => {
    const response = await api.get('/reports/category-breakdown');
    return response.data;
  },

  getMonthlyReport: async (year?: number, month?: number) => {
    const params = year && month ? { year, month } : {};
    const response = await api.get('/reports/monthly', { params });
    return response.data;
  },

  getLocationSummary: async () => {
    const response = await api.get('/reports/location-summary');
    return response.data;
  },
};

export default api;

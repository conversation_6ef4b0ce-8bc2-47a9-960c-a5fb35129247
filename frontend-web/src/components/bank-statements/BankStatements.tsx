import React, { useState } from 'react';
import {
  Upload,
  FileText,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';
import { bankStatementsAPI } from '../../services/api';

interface BankStatementEntry {
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
}

interface ParsedStatement {
  entries: BankStatementEntry[];
  filename: string;
}

const BankStatements: React.FC = () => {
  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [parsedStatement, setParsedStatement] = useState<ParsedStatement | null>(null);
  const [selectedEntries, setSelectedEntries] = useState<Set<number>>(new Set());
  const [error, setError] = useState('');

  const toggleEntrySelection = (index: number) => {
    const newSelected = new Set(selectedEntries);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedEntries(newSelected);
  };

  const selectAllEntries = () => {
    if (parsedStatement) {
      const allIndices = new Set(parsedStatement.entries.map((_, index) => index));
      setSelectedEntries(allIndices);
    }
  };

  const deselectAllEntries = () => {
    setSelectedEntries(new Set());
  };

  const importSelectedTransactions = async () => {
    if (!parsedStatement || selectedEntries.size === 0) return;

    try {
      const entriesToImport = parsedStatement.entries.filter((_, index) =>
        selectedEntries.has(index)
      );

      await bankStatementsAPI.import(entriesToImport);

      // Reset state after successful import
      setParsedStatement(null);
      setSelectedEntries(new Set());
    } catch (error: any) {
      console.error('Error importing transactions:', error);
      setError('Failed to import transactions. Please try again.');
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  };

  const handleFiles = async (files: FileList) => {
    const file = files[0];
    if (file && file.type === 'application/pdf') {
      await uploadFile(file);
    } else {
      setError('Please upload a PDF file');
    }
  };

  const uploadFile = async (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);
    setError('');

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Upload and parse the file
      const response = await bankStatementsAPI.upload(file);

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (response.data && response.data.entries) {
        setParsedStatement({
          entries: response.data.entries,
          filename: file.name
        });

        // Select all entries by default
        const allIndices = new Set<number>(response.data.entries.map((_: any, index: number) => index));
        setSelectedEntries(allIndices);
      }

    } catch (error: any) {
      console.error('Error uploading file:', error);
      setError(error.response?.data?.error || 'Failed to upload and parse PDF');
    } finally {
      setIsUploading(false);
      setTimeout(() => setUploadProgress(0), 1000);
    }
  };



  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Bank Statements</h1>
        <p className="text-gray-300">Upload and manage your bank statement PDFs</p>
      </div>

      {/* Upload Area */}
      <div className="bg-dark rounded-lg p-6 shadow-lg">
        <h3 className="text-lg font-semibold text-white mb-4">Upload New Statement</h3>

        {error && (
          <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-md">
            <p className="text-red-200 text-sm">{error}</p>
          </div>
        )}

        <div
          className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragActive
              ? 'border-primary bg-primary bg-opacity-10'
              : 'border-dark-400 hover:border-primary hover:bg-primary hover:bg-opacity-5'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <input
            type="file"
            accept=".pdf"
            onChange={handleChange}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            disabled={isUploading}
          />

          <div className="space-y-4">
            <div className="mx-auto w-12 h-12 bg-primary bg-opacity-20 rounded-lg flex items-center justify-center">
              <Upload className="h-6 w-6 text-primary" />
            </div>

            <div>
              <p className="text-lg font-medium text-white">
                {isUploading ? 'Processing PDF...' : 'Drop your PDF here, or click to browse'}
              </p>
              <p className="text-sm text-gray-400">
                Supports PDF files up to 10MB
              </p>
            </div>

            {isUploading && (
              <div className="w-full bg-background rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Parsed Transactions */}
      {parsedStatement && (
        <div className="bg-dark rounded-lg shadow-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-dark-400 flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-white">Parsed Transactions</h3>
              <p className="text-sm text-gray-400">From: {parsedStatement.filename}</p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={selectAllEntries}
                className="px-3 py-1 text-sm bg-primary text-white rounded hover:bg-primary-600 transition-colors"
              >
                Select All
              </button>
              <button
                onClick={deselectAllEntries}
                className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-500 transition-colors"
              >
                Deselect All
              </button>
              <button
                onClick={importSelectedTransactions}
                disabled={selectedEntries.size === 0}
                className="px-4 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Import Selected ({selectedEntries.size})
              </button>
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto">
            <table className="w-full">
              <thead className="bg-background sticky top-0">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Select
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Description
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Type
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-dark-400">
                {parsedStatement.entries.map((entry, index) => (
                  <tr
                    key={index}
                    className={`hover:bg-background transition-colors ${
                      selectedEntries.has(index) ? 'bg-primary bg-opacity-10' : ''
                    }`}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedEntries.has(index)}
                        onChange={() => toggleEntrySelection(index)}
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      {new Date(entry.date).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 text-sm text-white">
                      {entry.description}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`text-sm font-semibold ${
                          entry.type === 'income' ? 'text-green-400' : 'text-red-400'
                        }`}
                      >
                        {entry.type === 'income' ? '+' : '-'}₺{entry.amount.toFixed(2)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        entry.type === 'income'
                          ? 'bg-green-900/50 text-green-400'
                          : 'bg-red-900/50 text-red-400'
                      }`}>
                        {entry.type}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}



      {/* Processing Info */}
      <div className="bg-dark rounded-lg p-6 shadow-lg">
        <h3 className="text-lg font-semibold text-white mb-4">How it works</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="mx-auto w-12 h-12 bg-primary bg-opacity-20 rounded-lg flex items-center justify-center mb-3">
              <Upload className="h-6 w-6 text-primary" />
            </div>
            <h4 className="font-medium text-white mb-2">1. Upload PDF</h4>
            <p className="text-sm text-gray-400">
              Upload your bank statement PDF files securely
            </p>
          </div>
          <div className="text-center">
            <div className="mx-auto w-12 h-12 bg-secondary bg-opacity-20 rounded-lg flex items-center justify-center mb-3">
              <Clock className="h-6 w-6 text-secondary" />
            </div>
            <h4 className="font-medium text-white mb-2">2. AI Processing</h4>
            <p className="text-sm text-gray-400">
              Our AI extracts transaction data automatically
            </p>
          </div>
          <div className="text-center">
            <div className="mx-auto w-12 h-12 bg-accent bg-opacity-20 rounded-lg flex items-center justify-center mb-3">
              <CheckCircle className="h-6 w-6 text-accent" />
            </div>
            <h4 className="font-medium text-white mb-2">3. Review & Import</h4>
            <p className="text-sm text-gray-400">
              Review extracted transactions and import to your account
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BankStatements;

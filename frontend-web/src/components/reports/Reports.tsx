import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  PieChart,
  BarChart3,
  Calendar,
  Download
} from 'lucide-react';
import { reportsAPI } from '../../services/api';

const Reports: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [summary, setSummary] = useState<any>(null);
  const [categoryBreakdown, setCategoryBreakdown] = useState<any>(null);
  const [monthlyReport, setMonthlyReport] = useState<any>(null);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  useEffect(() => {
    const fetchReportsData = async () => {
      try {
        setLoading(true);

        // Fetch all reports data
        const [summaryResponse, categoryResponse, monthlyResponse] = await Promise.all([
          reportsAPI.getSummary('2020-01-01', '2030-12-31'),
          reportsAPI.getCategoryBreakdown(),
          reportsAPI.getMonthlyReport(selectedYear)
        ]);

        setSummary(summaryResponse.data);
        setCategoryBreakdown(categoryResponse.data);
        setMonthlyReport(monthlyResponse.data);

      } catch (error: any) {
        console.error('Error fetching reports data:', error);
        setError('Failed to load reports data');
      } finally {
        setLoading(false);
      }
    };

    fetchReportsData();
  }, [selectedYear]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-white">Reports</h1>
          <p className="text-gray-300">Loading financial reports...</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-dark rounded-lg p-6 shadow-lg animate-pulse">
              <div className="h-4 bg-gray-600 rounded w-3/4 mb-2"></div>
              <div className="h-32 bg-gray-600 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-white">Reports</h1>
          <p className="text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Financial Reports</h1>
          <p className="text-gray-300">Analyze your financial data and trends</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
            className="px-4 py-2 bg-dark border border-dark-400 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
          <button className="flex items-center px-4 py-2 bg-secondary text-white rounded-lg hover:bg-secondary-600 transition-colors">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-dark rounded-lg p-6 shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Total Income</p>
                <p className="text-2xl font-bold text-green-400">₺{summary.total_income?.toFixed(2) || '0.00'}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-400" />
            </div>
          </div>
          <div className="bg-dark rounded-lg p-6 shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Total Expenses</p>
                <p className="text-2xl font-bold text-red-400">₺{summary.total_expense?.toFixed(2) || '0.00'}</p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-400" />
            </div>
          </div>
          <div className="bg-dark rounded-lg p-6 shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Net Balance</p>
                <p className={`text-2xl font-bold ${summary.net_balance >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  ₺{summary.net_balance?.toFixed(2) || '0.00'}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-primary" />
            </div>
          </div>
          <div className="bg-dark rounded-lg p-6 shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Total Transactions</p>
                <p className="text-2xl font-bold text-white">{summary.total_transactions || 0}</p>
              </div>
              <PieChart className="h-8 w-8 text-accent" />
            </div>
          </div>
        </div>
      )}

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Category Breakdown */}
        <div className="bg-dark rounded-lg p-6 shadow-lg">
          <h3 className="text-lg font-semibold text-white mb-4">Expense Categories</h3>
          {categoryBreakdown?.expense_categories?.length > 0 ? (
            <div className="space-y-3">
              {categoryBreakdown.expense_categories.slice(0, 5).map((category: any, index: number) => (
                <div key={category.category} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full bg-red-${(index + 1) * 100}`}></div>
                    <span className="text-white font-medium">{category.category}</span>
                  </div>
                  <span className="text-white font-semibold">₺{category.amount.toFixed(2)}</span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-400 text-center py-8">No expense data available</p>
          )}
        </div>

        {/* Income Categories */}
        <div className="bg-dark rounded-lg p-6 shadow-lg">
          <h3 className="text-lg font-semibold text-white mb-4">Income Categories</h3>
          {categoryBreakdown?.income_categories?.length > 0 ? (
            <div className="space-y-3">
              {categoryBreakdown.income_categories.slice(0, 5).map((category: any, index: number) => (
                <div key={category.category} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full bg-green-${(index + 1) * 100}`}></div>
                    <span className="text-white font-medium">{category.category}</span>
                  </div>
                  <span className="text-white font-semibold">₺{category.amount.toFixed(2)}</span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-400 text-center py-8">No income data available</p>
          )}
        </div>
      </div>

      {/* Monthly Trends */}
      <div className="bg-dark rounded-lg p-6 shadow-lg">
        <h3 className="text-lg font-semibold text-white mb-4">Monthly Trends ({selectedYear})</h3>
        {monthlyReport?.months?.length > 0 ? (
          <div className="overflow-x-auto">
            <div className="flex space-x-4 min-w-full">
              {monthlyReport.months.map((month: any) => (
                <div key={month.month} className="flex-shrink-0 w-24 text-center">
                  <div className="text-xs text-gray-400 mb-2">{month.month.slice(0, 3)}</div>
                  <div className="relative h-32 bg-background rounded">
                    <div
                      className="absolute bottom-0 left-0 w-1/2 bg-green-500 rounded-l"
                      style={{
                        height: `${Math.max((month.income / Math.max(...monthlyReport.months.map((m: any) => Math.max(m.income, m.expense)))) * 100, 5)}%`
                      }}
                    ></div>
                    <div
                      className="absolute bottom-0 right-0 w-1/2 bg-red-500 rounded-r"
                      style={{
                        height: `${Math.max((month.expense / Math.max(...monthlyReport.months.map((m: any) => Math.max(m.income, m.expense)))) * 100, 5)}%`
                      }}
                    ></div>
                  </div>
                  <div className="mt-2 text-xs">
                    <div className="text-green-400">₺{month.income.toFixed(0)}</div>
                    <div className="text-red-400">₺{month.expense.toFixed(0)}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <p className="text-gray-400 text-center py-8">No monthly data available</p>
        )}
      </div>
    </div>
  );
};

export default Reports;

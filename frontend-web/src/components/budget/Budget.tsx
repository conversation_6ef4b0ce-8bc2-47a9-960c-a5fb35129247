import React, { useState, useEffect } from 'react';
import {
  Plus,
  Edit,
  Trash2,
  Target,
  TrendingUp,
  TrendingDown,
  AlertTriangle
} from 'lucide-react';
import { categoriesAPI, transactionsAPI } from '../../services/api';

interface BudgetItem {
  id: string;
  category_id: string;
  category_name: string;
  amount: number;
  spent: number;
  period: 'monthly' | 'yearly';
  status: 'on_track' | 'warning' | 'exceeded';
}

const Budget: React.FC = () => {
  const [budgets, setBudgets] = useState<BudgetItem[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [formData, setFormData] = useState({
    category_id: '',
    amount: '',
    period: 'monthly'
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch categories and transactions
        const [categoriesResponse, transactionsResponse] = await Promise.all([
          categoriesAPI.getAll(),
          transactionsAPI.getAll()
        ]);

        setCategories(categoriesResponse.data || []);

        // Calculate budgets based on categories and spending
        const mockBudgets = calculateBudgets(categoriesResponse.data || [], transactionsResponse.data || []);
        setBudgets(mockBudgets);

      } catch (error: any) {
        console.error('Error fetching data:', error);
        setError('Failed to load budget data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const calculateBudgets = (categories: any[], transactions: any[]): BudgetItem[] => {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    return categories.map(category => {
      // Calculate spent amount for current month
      const spent = transactions
        .filter(t => {
          const transactionDate = new Date(t.transaction_date);
          return t.category.id === category.id &&
                 t.type === 'expense' &&
                 transactionDate.getMonth() === currentMonth &&
                 transactionDate.getFullYear() === currentYear;
        })
        .reduce((sum, t) => sum + t.amount, 0);

      // Mock budget amounts (in real app, these would come from backend)
      const mockAmount = spent > 0 ? spent * 1.5 : 1000; // Set budget 50% higher than current spending
      
      const percentage = mockAmount > 0 ? (spent / mockAmount) * 100 : 0;
      let status: 'on_track' | 'warning' | 'exceeded' = 'on_track';
      
      if (percentage >= 100) status = 'exceeded';
      else if (percentage >= 80) status = 'warning';

      return {
        id: category.id,
        category_id: category.id,
        category_name: category.name,
        amount: mockAmount,
        spent,
        period: 'monthly' as const,
        status
      };
    }).filter(budget => budget.spent > 0 || budget.amount > 1000); // Only show categories with activity or set budgets
  };

  const handleAddBudget = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // In a real app, this would call a budget API
      const newBudget: BudgetItem = {
        id: Date.now().toString(),
        category_id: formData.category_id,
        category_name: categories.find(c => c.id === formData.category_id)?.name || '',
        amount: parseFloat(formData.amount),
        spent: 0,
        period: formData.period as 'monthly' | 'yearly',
        status: 'on_track'
      };

      setBudgets([...budgets, newBudget]);
      setShowAddModal(false);
      setFormData({
        category_id: '',
        amount: '',
        period: 'monthly'
      });
    } catch (error: any) {
      console.error('Error adding budget:', error);
      alert('Failed to add budget');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'exceeded': return 'text-red-400';
      case 'warning': return 'text-yellow-400';
      default: return 'text-green-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'exceeded': return <AlertTriangle className="h-5 w-5 text-red-400" />;
      case 'warning': return <TrendingUp className="h-5 w-5 text-yellow-400" />;
      default: return <Target className="h-5 w-5 text-green-400" />;
    }
  };

  const totalBudget = budgets.reduce((sum, budget) => sum + budget.amount, 0);
  const totalSpent = budgets.reduce((sum, budget) => sum + budget.spent, 0);
  const remainingBudget = totalBudget - totalSpent;

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-white">Budget</h1>
          <p className="text-gray-300">Loading budget data...</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-dark rounded-lg p-6 shadow-lg animate-pulse">
              <div className="h-4 bg-gray-600 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-600 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-white">Budget</h1>
          <p className="text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Budget Management</h1>
          <p className="text-gray-300">Track and manage your spending limits</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="mt-4 sm:mt-0 flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Budget
        </button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-dark rounded-lg p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Total Budget</p>
              <p className="text-2xl font-bold text-white">₺{totalBudget.toFixed(2)}</p>
            </div>
            <Target className="h-8 w-8 text-primary" />
          </div>
        </div>
        <div className="bg-dark rounded-lg p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Total Spent</p>
              <p className="text-2xl font-bold text-red-400">₺{totalSpent.toFixed(2)}</p>
            </div>
            <TrendingDown className="h-8 w-8 text-red-400" />
          </div>
        </div>
        <div className="bg-dark rounded-lg p-6 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Remaining</p>
              <p className={`text-2xl font-bold ${remainingBudget >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                ₺{remainingBudget.toFixed(2)}
              </p>
            </div>
            <TrendingUp className={`h-8 w-8 ${remainingBudget >= 0 ? 'text-green-400' : 'text-red-400'}`} />
          </div>
        </div>
      </div>

      {/* Budget List */}
      <div className="bg-dark rounded-lg shadow-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-dark-400">
          <h3 className="text-lg font-semibold text-white">Category Budgets</h3>
        </div>
        <div className="divide-y divide-dark-400">
          {budgets.length > 0 ? (
            budgets.map((budget) => {
              const percentage = budget.amount > 0 ? (budget.spent / budget.amount) * 100 : 0;
              return (
                <div key={budget.id} className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(budget.status)}
                      <div>
                        <h4 className="text-white font-medium">{budget.category_name}</h4>
                        <p className="text-sm text-gray-400">{budget.period} budget</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-white font-semibold">
                          ₺{budget.spent.toFixed(2)} / ₺{budget.amount.toFixed(2)}
                        </p>
                        <p className={`text-sm ${getStatusColor(budget.status)}`}>
                          {percentage.toFixed(1)}% used
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <button className="text-primary hover:text-primary-400 transition-colors">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="text-red-400 hover:text-red-300 transition-colors">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="w-full bg-background rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        budget.status === 'exceeded' ? 'bg-red-500' :
                        budget.status === 'warning' ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(percentage, 100)}%` }}
                    ></div>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="p-8 text-center">
              <Target className="h-12 w-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400">No budgets set yet</p>
              <p className="text-gray-500 text-sm mt-1">Create your first budget to start tracking your spending</p>
            </div>
          )}
        </div>
      </div>

      {/* Add Budget Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-dark rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Add Budget</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-white"
              >
                ×
              </button>
            </div>

            <form onSubmit={handleAddBudget} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Category
                </label>
                <select
                  name="category_id"
                  value={formData.category_id}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 bg-background border border-dark-400 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="">Select a category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Budget Amount
                </label>
                <input
                  type="number"
                  name="amount"
                  value={formData.amount}
                  onChange={handleInputChange}
                  required
                  step="0.01"
                  min="0"
                  className="w-full px-3 py-2 bg-background border border-dark-400 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="0.00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Period
                </label>
                <select
                  name="period"
                  value={formData.period}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-background border border-dark-400 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="monthly">Monthly</option>
                  <option value="yearly">Yearly</option>
                </select>
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
                >
                  Add Budget
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Budget;

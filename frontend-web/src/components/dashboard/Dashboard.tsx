import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  CreditCard,
  PieChart,
  BarChart3
} from 'lucide-react';
import { reportsAPI, transactionsAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [summary, setSummary] = useState<any>(null);
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);
  const [allTransactions, setAllTransactions] = useState<any[]>([]);
  const [error, setError] = useState('');
  const [chartType, setChartType] = useState<'pie' | 'bar'>('pie');

  // Calculate income vs expense breakdown
  const calculateIncomeExpenseBreakdown = () => {
    if (!allTransactions || allTransactions.length === 0) return [];

    // Calculate totals for income and expense
    const incomeTotal = allTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);

    const expenseTotal = allTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    const total = incomeTotal + expenseTotal;

    if (total === 0) return [];

    const breakdown = [
      {
        type: 'income',
        name: 'Income',
        total_amount: incomeTotal,
        transaction_count: allTransactions.filter(t => t.type === 'income').length,
        percentage: (incomeTotal / total) * 100,
        color: 'bg-green-500'
      },
      {
        type: 'expense',
        name: 'Expenses',
        total_amount: expenseTotal,
        transaction_count: allTransactions.filter(t => t.type === 'expense').length,
        percentage: (expenseTotal / total) * 100,
        color: 'bg-red-500'
      }
    ].filter(item => item.total_amount > 0); // Only show categories with transactions

    return breakdown;
  };

  const incomeExpenseBreakdown = calculateIncomeExpenseBreakdown();

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch summary data - get all time data
        const summaryResponse = await reportsAPI.getSummary('2020-01-01', '2030-12-31');
        setSummary(summaryResponse.data);

        // Fetch recent transactions
        const recentTransactionsResponse = await transactionsAPI.getAll({ limit: 5 });
        setRecentTransactions(recentTransactionsResponse.data || []);

        // Fetch all transactions for spending overview
        const allTransactionsResponse = await transactionsAPI.getAll();
        setAllTransactions(allTransactionsResponse.data || []);

      } catch (error: any) {
        console.error('Error fetching dashboard data:', error);
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Use real data from API or fallback to mock data
  const getChangeType = (change: string): 'positive' | 'negative' => {
    return change.startsWith('+') ? 'positive' : 'negative';
  };

  const stats = summary ? [
    {
      title: 'Total Balance',
      value: `₺${summary.total_balance?.toFixed(2) || '0.00'}`,
      change: summary.balance_change || '+0.0%',
      changeType: getChangeType(summary.balance_change || '+0.0%'),
      icon: DollarSign,
    },
    {
      title: 'Monthly Income',
      value: `₺${summary.monthly_income?.toFixed(2) || '0.00'}`,
      change: summary.income_change || '+0.0%',
      changeType: getChangeType(summary.income_change || '+0.0%'),
      icon: TrendingUp,
    },
    {
      title: 'Monthly Expenses',
      value: `₺${summary.monthly_expenses?.toFixed(2) || '0.00'}`,
      change: summary.expense_change || '+0.0%',
      changeType: summary.expense_change?.startsWith('-') ? 'negative' : 'positive',
      icon: TrendingDown,
    },
    {
      title: 'Total Transactions',
      value: summary.total_transactions?.toString() || '0',
      change: summary.transaction_change || '+0',
      changeType: getChangeType(summary.transaction_change || '+0'),
      icon: CreditCard,
    },
  ] : [
    {
      title: 'Total Balance',
      value: '$0.00',
      change: '+0.0%',
      changeType: 'positive' as const,
      icon: DollarSign,
    },
    {
      title: 'Monthly Income',
      value: '$0.00',
      change: '+0.0%',
      changeType: 'positive' as const,
      icon: TrendingUp,
    },
    {
      title: 'Monthly Expenses',
      value: '$0.00',
      change: '+0.0%',
      changeType: 'positive' as const,
      icon: TrendingDown,
    },
    {
      title: 'Total Transactions',
      value: '0',
      change: '+0',
      changeType: 'positive' as const,
      icon: CreditCard,
    },
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-white">Dashboard</h1>
          <p className="text-gray-300">Loading your financial overview...</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-dark rounded-lg p-6 shadow-lg animate-pulse">
              <div className="h-4 bg-gray-600 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-600 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-white">Dashboard</h1>
          <p className="text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Dashboard</h1>
        <p className="text-gray-300">
          Welcome back{user ? `, ${user.name}` : ''}! Here's your financial overview.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-dark rounded-lg p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">{stat.title}</p>
                  <p className="text-2xl font-bold text-white">{stat.value}</p>
                </div>
                <div className="p-3 bg-primary bg-opacity-20 rounded-lg">
                  <Icon className="h-6 w-6 text-primary" />
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <span
                  className={`text-sm font-medium ${
                    stat.changeType === 'positive' ? 'text-green-400' : 'text-red-400'
                  }`}
                >
                  {stat.change}
                </span>
                <span className="text-sm text-gray-400 ml-2">from last month</span>
              </div>
            </div>
          );
        })}
      </div>

      {/* Charts and Recent Transactions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Income vs Expense Overview */}
        <div className="bg-dark rounded-lg p-6 shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Income vs Expenses</h3>
            <div className="flex space-x-2">
              <button
                onClick={() => setChartType('pie')}
                className={`p-2 transition-colors ${
                  chartType === 'pie' ? 'text-primary' : 'text-gray-400 hover:text-white'
                }`}
              >
                <PieChart className="h-5 w-5" />
              </button>
              <button
                onClick={() => setChartType('bar')}
                className={`p-2 transition-colors ${
                  chartType === 'bar' ? 'text-primary' : 'text-gray-400 hover:text-white'
                }`}
              >
                <BarChart3 className="h-5 w-5" />
              </button>
            </div>
          </div>

          {loading ? (
            <div className="h-64 flex items-center justify-center bg-background rounded-lg">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                <p className="text-gray-400">Loading financial data...</p>
              </div>
            </div>
          ) : incomeExpenseBreakdown.length > 0 ? (
            <div className="space-y-6">
              {chartType === 'pie' ? (
                // Pie Chart Style
                <div className="space-y-4">
                  {/* Simple pie representation with percentages */}
                  <div className="flex items-center justify-center mb-6">
                    <div className="relative w-32 h-32">
                      {incomeExpenseBreakdown.map((item, index) => {
                        const circumference = 2 * Math.PI * 45; // radius = 45
                        const strokeDasharray = `${(item.percentage / 100) * circumference} ${circumference}`;
                        const rotation = index === 0 ? 0 : (incomeExpenseBreakdown[0]?.percentage || 0) * 3.6;

                        return (
                          <svg
                            key={item.type}
                            className="absolute inset-0 w-32 h-32 transform -rotate-90"
                            style={{ transform: `rotate(${rotation - 90}deg)` }}
                          >
                            <circle
                              cx="64"
                              cy="64"
                              r="45"
                              fill="none"
                              stroke={item.type === 'income' ? '#10B981' : '#EF4444'}
                              strokeWidth="12"
                              strokeDasharray={strokeDasharray}
                              strokeLinecap="round"
                            />
                          </svg>
                        );
                      })}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-lg font-bold text-white">
                            {incomeExpenseBreakdown.reduce((sum, item) => sum + item.transaction_count, 0)}
                          </div>
                          <div className="text-xs text-gray-400">Total</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Legend */}
                  <div className="space-y-3">
                    {incomeExpenseBreakdown.map((item) => (
                      <div key={item.type} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-4 h-4 rounded-full ${item.type === 'income' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                          <span className="text-white font-medium">{item.name}</span>
                          <span className="text-gray-500 text-sm">
                            ({item.transaction_count} transactions)
                          </span>
                        </div>
                        <div className="text-right">
                          <span className="text-white font-semibold">
                            ₺{item.total_amount.toFixed(2)}
                          </span>
                          <span className="text-gray-400 text-sm ml-2">
                            ({item.percentage.toFixed(1)}%)
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                // Bar Chart Style
                <div className="space-y-4">
                  <div className="h-48 flex items-end justify-center space-x-8 bg-background rounded-lg p-4">
                    {incomeExpenseBreakdown.map((item) => (
                      <div key={item.type} className="flex flex-col items-center space-y-2">
                        <div className="text-xs text-gray-400 mb-1">
                          ₺{item.total_amount.toFixed(0)}
                        </div>
                        <div
                          className={`w-16 ${item.type === 'income' ? 'bg-green-500' : 'bg-red-500'} rounded-t`}
                          style={{
                            height: `${Math.max((item.percentage / 100) * 120, 20)}px`,
                            transition: 'height 0.3s ease'
                          }}
                        ></div>
                        <div className="text-sm text-white font-medium">{item.name}</div>
                        <div className="text-xs text-gray-400">
                          {item.percentage.toFixed(1)}%
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Summary */}
                  <div className="grid grid-cols-2 gap-4">
                    {incomeExpenseBreakdown.map((item) => (
                      <div key={item.type} className="bg-background rounded-lg p-3">
                        <div className="flex items-center space-x-2 mb-1">
                          <div className={`w-3 h-3 rounded-full ${item.type === 'income' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                          <span className="text-white text-sm font-medium">{item.name}</span>
                        </div>
                        <div className="text-white font-semibold">₺{item.total_amount.toFixed(2)}</div>
                        <div className="text-gray-400 text-xs">
                          {item.transaction_count} transactions
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center bg-background rounded-lg">
              <div className="text-center">
                <PieChart className="h-12 w-12 text-gray-500 mx-auto mb-2" />
                <p className="text-gray-400">No financial data available</p>
                <p className="text-gray-500 text-sm mt-1">Add some transactions to see your income vs expenses</p>
              </div>
            </div>
          )}
        </div>

        {/* Recent Transactions */}
        <div className="bg-dark rounded-lg p-6 shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Recent Transactions</h3>
            <button
              onClick={() => navigate('/transactions')}
              className="text-primary hover:text-primary-400 text-sm font-medium transition-colors"
            >
              View All
            </button>
          </div>
          <div className="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
            <div className="space-y-3 pr-2">
              {recentTransactions.length > 0 ? (
                recentTransactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between p-3 bg-background rounded-lg">
                    <div className="flex-1 min-w-0">
                      <p className="text-white font-medium truncate" title={transaction.title}>
                        {transaction.title}
                      </p>
                      <p className="text-sm text-gray-400">
                        {transaction.category?.name || 'Uncategorized'} • {new Date(transaction.transaction_date).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right ml-4 flex-shrink-0">
                      <p
                        className={`font-semibold ${
                          transaction.type === 'income' ? 'text-green-400' : 'text-red-400'
                        }`}
                      >
                        {transaction.type === 'income' ? '+' : '-'}₺{transaction.amount.toFixed(2)}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-400">No recent transactions</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-dark rounded-lg p-6 shadow-lg">
        <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => navigate('/transactions?action=add')}
            className="flex items-center justify-center p-4 bg-primary bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors"
          >
            <CreditCard className="h-5 w-5 text-primary mr-2" />
            <span className="text-white font-medium">Add Transaction</span>
          </button>
          <button
            onClick={() => navigate('/reports')}
            className="flex items-center justify-center p-4 bg-secondary bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors"
          >
            <TrendingUp className="h-5 w-5 text-secondary mr-2" />
            <span className="text-white font-medium">View Reports</span>
          </button>
          <button
            onClick={() => navigate('/budget')}
            className="flex items-center justify-center p-4 bg-accent bg-opacity-20 rounded-lg hover:bg-opacity-30 transition-colors"
          >
            <DollarSign className="h-5 w-5 text-accent mr-2" />
            <span className="text-white font-medium">Set Budget</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

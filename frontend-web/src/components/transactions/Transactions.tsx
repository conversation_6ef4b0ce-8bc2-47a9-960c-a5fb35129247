import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Plus,
  Search,
  Filter,
  Download,
  Edit,
  Trash2,
  X
} from 'lucide-react';
import { transactionsAPI, categoriesAPI, accountsAPI } from '../../services/api';

interface Transaction {
  id: string;
  title: string;
  amount: number;
  transaction_date: string;
  category: {
    id: string;
    name: string;
  };
  account: {
    id: string;
    name: string;
  };
  type: 'income' | 'expense';
  payment_method: string;
  note?: string;
}

const Transactions: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [accounts, setAccounts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    amount: '',
    type: 'expense',
    currency: 'TL',
    category_id: '',
    account_id: '',
    transaction_date: new Date().toISOString().split('T')[0],
    payment_method: 'cash',
    note: ''
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch transactions, categories, and accounts
        const [transactionsResponse, categoriesResponse, accountsResponse] = await Promise.all([
          transactionsAPI.getAll(),
          categoriesAPI.getAll(),
          accountsAPI.getAll()
        ]);

        setTransactions(transactionsResponse.data || []);
        setCategories(categoriesResponse.data || []);
        setAccounts(accountsResponse.data || []);

      } catch (error: any) {
        console.error('Error fetching data:', error);
        setError('Failed to load transactions');
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Check if we should open the add modal
    const action = searchParams.get('action');
    if (action === 'add') {
      setShowAddModal(true);
      // Remove the action parameter from URL
      setSearchParams({});
    }
  }, [searchParams, setSearchParams]);

  const handleEditTransaction = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setFormData({
      title: transaction.title,
      amount: transaction.amount.toString(),
      type: transaction.type,
      currency: (transaction as any).currency || 'TL', // Default to TL if currency not available
      category_id: transaction.category.id,
      account_id: transaction.account.id,
      transaction_date: new Date(transaction.transaction_date).toISOString().split('T')[0],
      payment_method: transaction.payment_method,
      note: transaction.note || ''
    });
    setShowEditModal(true);
  };

  const handleDeleteClick = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedTransaction) return;

    try {
      await transactionsAPI.delete(selectedTransaction.id);
      setTransactions(transactions.filter(t => t.id !== selectedTransaction.id));
      setShowDeleteModal(false);
      setSelectedTransaction(null);
    } catch (error: any) {
      console.error('Error deleting transaction:', error);
      const errorMessage = error.response?.data?.error || error.message || 'Failed to delete transaction';
      alert(`Failed to delete transaction: ${errorMessage}`);
    }
  };

  const handleAddTransaction = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const transactionData = {
        ...formData,
        amount: parseFloat(formData.amount),
        transaction_date: new Date(formData.transaction_date).toISOString()
      };

      console.log('Sending transaction data:', transactionData);
      const response = await transactionsAPI.create(transactionData);
      setTransactions([response.data, ...transactions]);
      setShowAddModal(false);
      setFormData({
        title: '',
        amount: '',
        type: 'expense',
        currency: 'TL',
        category_id: '',
        account_id: '',
        transaction_date: new Date().toISOString().split('T')[0],
        payment_method: 'cash',
        note: ''
      });
    } catch (error: any) {
      console.error('Error adding transaction:', error);
      const errorMessage = error.response?.data?.error || error.message || 'Failed to add transaction';
      alert(`Failed to add transaction: ${errorMessage}`);
    }
  };

  const handleUpdateTransaction = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedTransaction) return;

    try {
      const transactionData = {
        ...formData,
        amount: parseFloat(formData.amount),
        transaction_date: new Date(formData.transaction_date).toISOString()
      };

      console.log('Updating transaction data:', transactionData);
      const response = await transactionsAPI.update(selectedTransaction.id, transactionData);
      setTransactions(transactions.map(t =>
        t.id === selectedTransaction.id ? response.data : t
      ));
      setShowEditModal(false);
      setSelectedTransaction(null);
      setFormData({
        title: '',
        amount: '',
        type: 'expense',
        currency: 'TL',
        category_id: '',
        account_id: '',
        transaction_date: new Date().toISOString().split('T')[0],
        payment_method: 'cash',
        note: ''
      });
    } catch (error: any) {
      console.error('Error updating transaction:', error);
      const errorMessage = error.response?.data?.error || error.message || 'Failed to update transaction';
      alert(`Failed to update transaction: ${errorMessage}`);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const categoryOptions = ['all', ...categories.map(cat => cat.name)];
  const types = ['all', 'income', 'expense'];

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = transaction.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.category.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || transaction.category.name === selectedCategory;
    const matchesType = selectedType === 'all' || transaction.type === selectedType;

    return matchesSearch && matchesCategory && matchesType;
  });

  const totalIncome = transactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalExpenses = transactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  const netAmount = totalIncome - totalExpenses;

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-white">Transactions</h1>
          <p className="text-gray-300">Loading transactions...</p>
        </div>
        <div className="bg-dark rounded-lg p-6 shadow-lg animate-pulse">
          <div className="h-4 bg-gray-600 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="h-12 bg-gray-600 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-white">Transactions</h1>
          <p className="text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Transactions</h1>
          <p className="text-gray-300">Manage your financial transactions</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button className="flex items-center px-4 py-2 bg-secondary text-white rounded-lg hover:bg-secondary-600 transition-colors">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Transaction
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-dark rounded-lg p-6 shadow-lg">
          <h3 className="text-sm font-medium text-gray-400">Total Income</h3>
          <p className="text-2xl font-bold text-green-400">₺{totalIncome.toFixed(2)}</p>
        </div>
        <div className="bg-dark rounded-lg p-6 shadow-lg">
          <h3 className="text-sm font-medium text-gray-400">Total Expenses</h3>
          <p className="text-2xl font-bold text-red-400">₺{totalExpenses.toFixed(2)}</p>
        </div>
        <div className="bg-dark rounded-lg p-6 shadow-lg">
          <h3 className="text-sm font-medium text-gray-400">Net Amount</h3>
          <p className={`text-2xl font-bold ${netAmount >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            ₺{netAmount.toFixed(2)}
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-dark rounded-lg p-6 shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search transactions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-background border border-dark-400 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 bg-background border border-dark-400 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            {categoryOptions.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'All Categories' : category}
              </option>
            ))}
          </select>

          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-4 py-2 bg-background border border-dark-400 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            {types.map(type => (
              <option key={type} value={type}>
                {type === 'all' ? 'All Types' : type.charAt(0).toUpperCase() + type.slice(1)}
              </option>
            ))}
          </select>

          <button className="flex items-center justify-center px-4 py-2 bg-accent text-white rounded-lg hover:bg-accent-600 transition-colors">
            <Filter className="h-4 w-4 mr-2" />
            More Filters
          </button>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="bg-dark rounded-lg shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-background">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider w-1/4">
                  Description
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider w-1/6">
                  Category
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider w-1/6">
                  Account
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider w-1/8">
                  Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider w-1/8">
                  Amount
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider w-1/8">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-400">
              {filteredTransactions.length > 0 ? (
                filteredTransactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-background transition-colors">
                    <td className="px-4 py-4">
                      <div className="max-w-xs">
                        <div
                          className="text-sm font-medium text-white truncate"
                          title={transaction.title}
                        >
                          {transaction.title}
                        </div>
                        {transaction.note && (
                          <div
                            className="text-xs text-gray-400 truncate mt-1"
                            title={transaction.note}
                          >
                            {transaction.note}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-secondary bg-opacity-20 text-secondary">
                        {transaction.category.name}
                      </span>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-300">
                      <div className="truncate max-w-24" title={transaction.account.name}>
                        {transaction.account.name}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-300">
                      {new Date(transaction.transaction_date).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <span
                        className={`text-sm font-semibold ${
                          transaction.type === 'income' ? 'text-green-400' : 'text-red-400'
                        }`}
                      >
                        {transaction.type === 'income' ? '+' : '-'}₺{transaction.amount.toFixed(2)}
                      </span>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEditTransaction(transaction)}
                          className="text-primary hover:text-primary-400 transition-colors"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteClick(transaction)}
                          className="text-red-400 hover:text-red-300 transition-colors"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="px-4 py-8 text-center text-gray-400">
                    No transactions found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Transaction Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-dark rounded-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-dark flex items-center justify-between p-4 border-b border-dark-400">
              <h3 className="text-lg font-semibold text-white">Add Transaction</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <form id="add-transaction-form" onSubmit={handleAddTransaction} className="p-4 space-y-3">
              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">
                  Title
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  placeholder="Enter transaction title"
                />
              </div>

              <div className="grid grid-cols-3 gap-2">
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    Amount
                  </label>
                  <input
                    type="number"
                    name="amount"
                    value={formData.amount}
                    onChange={handleInputChange}
                    required
                    step="0.01"
                    min="0"
                    className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    Currency
                  </label>
                  <select
                    name="currency"
                    value={formData.currency}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  >
                    <option value="TL">TL</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                  </select>
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    Type
                  </label>
                  <select
                    name="type"
                    value={formData.type}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  >
                    <option value="expense">Expense</option>
                    <option value="income">Income</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    Category
                  </label>
                  <select
                    name="category_id"
                    value={formData.category_id}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  >
                    <option value="">Select category</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    Account
                  </label>
                  <select
                    name="account_id"
                    value={formData.account_id}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  >
                    <option value="">Select account</option>
                    {accounts.map(account => (
                      <option key={account.id} value={account.id}>
                        {account.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    Date
                  </label>
                  <input
                    type="date"
                    name="transaction_date"
                    value={formData.transaction_date}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    Payment Method
                  </label>
                  <select
                    name="payment_method"
                    value={formData.payment_method}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  >
                    <option value="cash">Cash</option>
                    <option value="card">Card</option>
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="check">Check</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">
                  Note (Optional)
                </label>
                <textarea
                  name="note"
                  value={formData.note}
                  onChange={handleInputChange}
                  rows={2}
                  className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  placeholder="Add a note..."
                />
              </div>

            </form>

            <div className="sticky bottom-0 bg-dark border-t border-dark-400 p-4">
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="flex-1 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors text-sm"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  form="add-transaction-form"
                  className="flex-1 px-4 py-2 bg-primary text-white rounded hover:bg-primary-600 transition-colors text-sm"
                >
                  Add Transaction
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Transaction Modal */}
      {showEditModal && selectedTransaction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-dark rounded-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-dark flex items-center justify-between p-4 border-b border-dark-400">
              <h3 className="text-lg font-semibold text-white">Edit Transaction</h3>
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setSelectedTransaction(null);
                }}
                className="text-gray-400 hover:text-white"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <form id="edit-transaction-form" onSubmit={handleUpdateTransaction} className="p-4 space-y-3">
              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">
                  Title
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  placeholder="Enter transaction title"
                />
              </div>

              <div className="grid grid-cols-3 gap-2">
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    Amount
                  </label>
                  <input
                    type="number"
                    name="amount"
                    value={formData.amount}
                    onChange={handleInputChange}
                    required
                    step="0.01"
                    min="0"
                    className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    Currency
                  </label>
                  <select
                    name="currency"
                    value={formData.currency}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  >
                    <option value="TL">TL</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                  </select>
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    Type
                  </label>
                  <select
                    name="type"
                    value={formData.type}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  >
                    <option value="expense">Expense</option>
                    <option value="income">Income</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    Category
                  </label>
                  <select
                    name="category_id"
                    value={formData.category_id}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  >
                    <option value="">Select category</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    Account
                  </label>
                  <select
                    name="account_id"
                    value={formData.account_id}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  >
                    <option value="">Select account</option>
                    {accounts.map(account => (
                      <option key={account.id} value={account.id}>
                        {account.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    Date
                  </label>
                  <input
                    type="date"
                    name="transaction_date"
                    value={formData.transaction_date}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">
                    Payment Method
                  </label>
                  <select
                    name="payment_method"
                    value={formData.payment_method}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  >
                    <option value="cash">Cash</option>
                    <option value="card">Card</option>
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="check">Check</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">
                  Note (Optional)
                </label>
                <textarea
                  name="note"
                  value={formData.note}
                  onChange={handleInputChange}
                  rows={2}
                  className="w-full px-3 py-2 bg-background border border-dark-400 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-transparent"
                  placeholder="Add a note..."
                />
              </div>
            </form>

            <div className="sticky bottom-0 bg-dark border-t border-dark-400 p-4">
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setShowEditModal(false);
                    setSelectedTransaction(null);
                  }}
                  className="flex-1 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors text-sm"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  form="edit-transaction-form"
                  className="flex-1 px-4 py-2 bg-primary text-white rounded hover:bg-primary-600 transition-colors text-sm"
                >
                  Update Transaction
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && selectedTransaction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-dark rounded-lg w-full max-w-md mx-4">
            <div className="p-6">
              <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full">
                <Trash2 className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-white text-center mb-2">
                Delete Transaction
              </h3>
              <p className="text-gray-300 text-center mb-6">
                Are you sure you want to delete "{selectedTransaction.title}"? This action cannot be undone.
              </p>
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setSelectedTransaction(null);
                  }}
                  className="flex-1 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleDeleteConfirm}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Transactions;

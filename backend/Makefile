build:
	go build -o fin_notebook  main.go

swagger:
	swag init

hot:
	@echo "Starting backend and frontend development servers..."
	@echo "Backend will run on http://localhost:8008"
	@echo "Frontend will run on http://localhost:3000"
	@echo "Press Ctrl+C to stop both servers"
	@make -j2 hot-backend

hot-backend:
	@echo "Starting backend with Docker..."
	docker compose -p fin_notebook -f docker-compose.yml up

hot-frontend:
	@echo "Starting frontend development server..."
	cd ../frontend-web && npm start

run:
	docker compose -f docker-compose.yml up -d --build

down:
	docker compose -f docker-compose.yml down
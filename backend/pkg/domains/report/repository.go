package report

import (
	"fmt"
	"time"

	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/entities"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	GetSummary(userID uuid.UUID, startDate, endDate time.Time) (*dtos.SummaryReportResponse, error)
	GetCategoryBreakdown(userID uuid.UUID, startDate, endDate time.Time) (*dtos.CategoryBreakdownResponse, error)
	GetMonthlyReport(userID uuid.UUID, year int) (*dtos.MonthlyReportResponse, error)
	GetLocationSummary(userID uuid.UUID, startDate, endDate time.Time) (*dtos.LocationSummaryResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) GetSummary(userID uuid.UUID, startDate, endDate time.Time) (*dtos.SummaryReportResponse, error) {
	var totalIncome, totalExpense float64
	var totalTransactions int64

	// Get total income
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND type = ? AND transaction_date BETWEEN ? AND ?", userID, "income", startDate, endDate).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&totalIncome).Error; err != nil {
		return nil, err
	}

	// Get total expense
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND type = ? AND transaction_date BETWEEN ? AND ?", userID, "expense", startDate, endDate).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&totalExpense).Error; err != nil {
		return nil, err
	}

	// Get total transaction count
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND transaction_date BETWEEN ? AND ?", userID, startDate, endDate).
		Count(&totalTransactions).Error; err != nil {
		return nil, err
	}

	// Calculate current month's income and expenses
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	monthEnd := monthStart.AddDate(0, 1, -1)

	var monthlyIncome, monthlyExpenses float64
	var monthlyTransactions int64

	// Get current month income
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND type = ? AND transaction_date BETWEEN ? AND ?", userID, "income", monthStart, monthEnd).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&monthlyIncome).Error; err != nil {
		return nil, err
	}

	// Get current month expenses
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND type = ? AND transaction_date BETWEEN ? AND ?", userID, "expense", monthStart, monthEnd).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&monthlyExpenses).Error; err != nil {
		return nil, err
	}

	// Get current month transaction count
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND transaction_date BETWEEN ? AND ?", userID, monthStart, monthEnd).
		Count(&monthlyTransactions).Error; err != nil {
		return nil, err
	}

	// Calculate previous month's data for comparison
	prevMonthStart := monthStart.AddDate(0, -1, 0)
	prevMonthEnd := prevMonthStart.AddDate(0, 1, -1)

	var prevMonthlyIncome, prevMonthlyExpenses float64
	var prevMonthlyTransactions int64

	// Get previous month income
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND type = ? AND transaction_date BETWEEN ? AND ?", userID, "income", prevMonthStart, prevMonthEnd).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&prevMonthlyIncome).Error; err != nil {
		return nil, err
	}

	// Get previous month expenses
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND type = ? AND transaction_date BETWEEN ? AND ?", userID, "expense", prevMonthStart, prevMonthEnd).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&prevMonthlyExpenses).Error; err != nil {
		return nil, err
	}

	// Get previous month transaction count
	if err := r.db.Model(&entities.Transaction{}).
		Where("user_id = ? AND transaction_date BETWEEN ? AND ?", userID, prevMonthStart, prevMonthEnd).
		Count(&prevMonthlyTransactions).Error; err != nil {
		return nil, err
	}

	// Get top expense categories
	type CategoryAmount struct {
		CategoryID   uuid.UUID
		CategoryName string
		Amount       float64
	}

	var topExpenseCategories []CategoryAmount
	if err := r.db.Model(&entities.Transaction{}).
		Select("transactions.category_id, categories.name as category_name, COALESCE(SUM(transactions.amount), 0) as amount").
		Joins("JOIN categories ON transactions.category_id = categories.id").
		Where("transactions.user_id = ? AND transactions.type = ? AND transactions.transaction_date BETWEEN ? AND ?", userID, "expense", startDate, endDate).
		Group("transactions.category_id, categories.name").
		Order("amount DESC").
		Limit(5).
		Scan(&topExpenseCategories).Error; err != nil {
		return nil, err
	}

	// Get top income categories
	var topIncomeCategories []CategoryAmount
	if err := r.db.Model(&entities.Transaction{}).
		Select("transactions.category_id, categories.name as category_name, COALESCE(SUM(transactions.amount), 0) as amount").
		Joins("JOIN categories ON transactions.category_id = categories.id").
		Where("transactions.user_id = ? AND transactions.type = ? AND transactions.transaction_date BETWEEN ? AND ?", userID, "income", startDate, endDate).
		Group("transactions.category_id, categories.name").
		Order("amount DESC").
		Limit(5).
		Scan(&topIncomeCategories).Error; err != nil {
		return nil, err
	}

	// Calculate percentage changes
	balanceChange := r.calculatePercentageChange(monthlyIncome-monthlyExpenses, prevMonthlyIncome-prevMonthlyExpenses)
	incomeChange := r.calculatePercentageChange(monthlyIncome, prevMonthlyIncome)
	expenseChange := r.calculatePercentageChange(monthlyExpenses, prevMonthlyExpenses)
	transactionChange := r.calculateTransactionChange(int(monthlyTransactions), int(prevMonthlyTransactions))

	// Convert to response
	response := &dtos.SummaryReportResponse{
		TotalIncome:       totalIncome,
		TotalExpense:      totalExpense,
		NetBalance:        totalIncome - totalExpense,
		TotalBalance:      totalIncome - totalExpense, // Same as net balance for now
		MonthlyIncome:     monthlyIncome,
		MonthlyExpenses:   monthlyExpenses,
		TotalTransactions: int(totalTransactions),
		BalanceChange:     balanceChange,
		IncomeChange:      incomeChange,
		ExpenseChange:     expenseChange,
		TransactionChange: transactionChange,
	}

	for _, category := range topExpenseCategories {
		response.TopExpenseCategories = append(response.TopExpenseCategories, dtos.CategoryAmountDTO{
			Category: category.CategoryName,
			Amount:   category.Amount,
		})
	}

	for _, category := range topIncomeCategories {
		response.TopIncomeCategories = append(response.TopIncomeCategories, dtos.CategoryAmountDTO{
			Category: category.CategoryName,
			Amount:   category.Amount,
		})
	}

	return response, nil
}

func (r *repository) GetCategoryBreakdown(userID uuid.UUID, startDate, endDate time.Time) (*dtos.CategoryBreakdownResponse, error) {
	type CategoryAmount struct {
		CategoryID   uuid.UUID
		CategoryName string
		Amount       float64
	}

	// Get expense categories
	var expenseCategories []CategoryAmount
	if err := r.db.Model(&entities.Transaction{}).
		Select("transactions.category_id, categories.name as category_name, COALESCE(SUM(transactions.amount), 0) as amount").
		Joins("JOIN categories ON transactions.category_id = categories.id").
		Where("transactions.user_id = ? AND transactions.type = ? AND transactions.transaction_date BETWEEN ? AND ?", userID, "expense", startDate, endDate).
		Group("transactions.category_id, categories.name").
		Order("amount DESC").
		Scan(&expenseCategories).Error; err != nil {
		return nil, err
	}

	// Get income categories
	var incomeCategories []CategoryAmount
	if err := r.db.Model(&entities.Transaction{}).
		Select("transactions.category_id, categories.name as category_name, COALESCE(SUM(transactions.amount), 0) as amount").
		Joins("JOIN categories ON transactions.category_id = categories.id").
		Where("transactions.user_id = ? AND transactions.type = ? AND transactions.transaction_date BETWEEN ? AND ?", userID, "income", startDate, endDate).
		Group("transactions.category_id, categories.name").
		Order("amount DESC").
		Scan(&incomeCategories).Error; err != nil {
		return nil, err
	}

	// Convert to response
	response := &dtos.CategoryBreakdownResponse{}

	for _, category := range expenseCategories {
		response.ExpenseCategories = append(response.ExpenseCategories, dtos.CategoryAmountDTO{
			Category: category.CategoryName,
			Amount:   category.Amount,
		})
	}

	for _, category := range incomeCategories {
		response.IncomeCategories = append(response.IncomeCategories, dtos.CategoryAmountDTO{
			Category: category.CategoryName,
			Amount:   category.Amount,
		})
	}

	return response, nil
}

func (r *repository) GetMonthlyReport(userID uuid.UUID, year int) (*dtos.MonthlyReportResponse, error) {
	type MonthlyData struct {
		Month   int
		Income  float64
		Expense float64
	}

	var monthlyData []MonthlyData

	// Get monthly income and expense
	if err := r.db.Model(&entities.Transaction{}).
		Select("EXTRACT(MONTH FROM transaction_date) as month, "+
			"COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as income, "+
			"COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as expense").
		Where("user_id = ? AND EXTRACT(YEAR FROM transaction_date) = ?", userID, year).
		Group("month").
		Order("month").
		Scan(&monthlyData).Error; err != nil {
		return nil, err
	}

	// Convert to response
	response := &dtos.MonthlyReportResponse{}

	// Ensure all months are included
	monthNames := []string{"January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"}
	monthDataMap := make(map[int]MonthlyData)

	for _, data := range monthlyData {
		monthDataMap[data.Month] = data
	}

	for i := 1; i <= 12; i++ {
		data, exists := monthDataMap[i]
		if !exists {
			data = MonthlyData{Month: i, Income: 0, Expense: 0}
		}

		response.Months = append(response.Months, dtos.MonthlyDataDTO{
			Month:   monthNames[i-1],
			Income:  data.Income,
			Expense: data.Expense,
		})
	}

	return response, nil
}

func (r *repository) GetLocationSummary(userID uuid.UUID, startDate, endDate time.Time) (*dtos.LocationSummaryResponse, error) {
	type LocationAmount struct {
		Location string
		Amount   float64
	}

	var locationAmounts []LocationAmount

	// Get location summary
	if err := r.db.Model(&entities.Transaction{}).
		Select("location, COALESCE(SUM(amount), 0) as amount").
		Where("user_id = ? AND type = ? AND transaction_date BETWEEN ? AND ? AND location != ''", userID, "expense", startDate, endDate).
		Group("location").
		Order("amount DESC").
		Scan(&locationAmounts).Error; err != nil {
		return nil, err
	}

	// Convert to response
	response := &dtos.LocationSummaryResponse{}

	for _, location := range locationAmounts {
		response.Locations = append(response.Locations, dtos.LocationAmountDTO{
			Location: location.Location,
			Amount:   location.Amount,
		})
	}

	return response, nil
}

// calculatePercentageChange calculates the percentage change between current and previous values
func (r *repository) calculatePercentageChange(current, previous float64) string {
	if previous == 0 {
		if current > 0 {
			return "+100.0%"
		} else if current < 0 {
			return "-100.0%"
		}
		return "+0.0%"
	}

	change := ((current - previous) / previous) * 100
	if change >= 0 {
		return fmt.Sprintf("+%.1f%%", change)
	}
	return fmt.Sprintf("%.1f%%", change)
}

// calculateTransactionChange calculates the change in transaction count
func (r *repository) calculateTransactionChange(current, previous int) string {
	change := current - previous
	if change >= 0 {
		return fmt.Sprintf("+%d", change)
	}
	return fmt.Sprintf("%d", change)
}

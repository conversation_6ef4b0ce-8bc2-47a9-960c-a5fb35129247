package bank_statement

import (
	"bufio"
	"bytes"
	"fmt"
	"io"
	"math"
	"mime/multipart"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/NocyTech/fin_notebook/pkg/domains/account"
	"github.com/NocyTech/fin_notebook/pkg/domains/category"
	"github.com/NocyTech/fin_notebook/pkg/domains/transaction"
	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/utils"
	"github.com/ledongthuc/pdf"
)

// Service defines the interface for bank statement operations
type Service interface {
	ParseVakifbankStatement(userID string, file multipart.File) ([]dtos.BankStatementEntry, error)
	ImportBankStatementEntries(userID string, entries []dtos.BankStatementImportRequest) ([]dtos.TransactionResponse, error)
}

// service implements the Service interface
type service struct {
	repository         Repository
	transactionService transaction.Service
	categoryService    category.Service
	accountService     account.Service
}

// NewService creates a new bank statement service
func NewService(r Repository, ts transaction.Service, cs category.Service, as account.Service) Service {
	return &service{
		repository:         r,
		transactionService: ts,
		categoryService:    cs,
		accountService:     as,
	}
}

// getCategoryIDForTransaction finds the best matching category ID from the user's categories
func (s *service) getCategoryIDForTransaction(userID, description, transactionType string) string {
	// Get all categories for the user
	categories, err := s.categoryService.GetAllCategories(userID, transactionType)
	if err != nil {
		fmt.Printf("Error getting categories: %v\n", err)
		// Fallback to hardcoded category matching
		return utils.GetCategoryIDForTransaction(description, transactionType)
	}

	// Convert description to lowercase for case-insensitive matching
	lowerDesc := strings.ToLower(description)

	// Define keywords for different category types
	categoryKeywords := map[string][]string{
		// Income keywords
		"salary":     {"maaş", "salary", "ücret", "wage"},
		"transfer":   {"gelen eft", "gelen havale", "gelen fast", "transfer", "virman"},
		"interest":   {"faiz", "interest"},
		"dividend":   {"temettü", "dividend"},
		"rent":       {"kira geliri", "rent income"},
		"freelance":  {"freelance", "serbest", "danışmanlık", "consulting"},
		"investment": {"yatırım", "investment", "hisse", "stock"},

		// Expense keywords
		"groceries":      {"market", "süpermarket", "grocery", "migros", "carrefour", "bim", "a101"},
		"dining":         {"restoran", "restaurant", "cafe", "kafe", "yemek", "food", "mcdonalds", "burger"},
		"transportation": {"akaryakıt", "benzin", "fuel", "gas", "taksi", "taxi", "ulaşım", "transport", "metro", "bus"},
		"utilities":      {"elektrik", "electricity", "su", "water", "doğalgaz", "gas bill", "telefon", "phone", "internet"},
		"housing":        {"kira", "rent", "aidat", "mortgage", "ev", "house"},
		"healthcare":     {"sağlık", "health", "hastane", "hospital", "doktor", "doctor", "eczane", "pharmacy"},
		"education":      {"eğitim", "education", "okul", "school", "üniversite", "university", "kurs", "course"},
		"shopping":       {"alışveriş", "shopping", "mağaza", "store", "online", "amazon", "trendyol"},
		"entertainment":  {"eğlence", "entertainment", "sinema", "cinema", "konser", "concert", "oyun", "game"},
		"insurance":      {"sigorta", "insurance"},
		"tax":            {"vergi", "tax"},
		"fee":            {"masraf", "fee", "komisyon", "commission"},
		"withdrawal":     {"atm", "para çek", "withdrawal", "nakit"},
	}

	// First, try to find exact or partial matches with category names
	for _, category := range categories {
		categoryNameLower := strings.ToLower(category.Name)

		// Check if category name appears in description
		if strings.Contains(lowerDesc, categoryNameLower) || strings.Contains(categoryNameLower, lowerDesc) {
			return category.ID
		}
	}

	// Then, try keyword matching
	for categoryType, keywords := range categoryKeywords {
		for _, keyword := range keywords {
			if strings.Contains(lowerDesc, keyword) {
				// Find a category that matches this type
				for _, category := range categories {
					categoryNameLower := strings.ToLower(category.Name)
					if strings.Contains(categoryNameLower, categoryType) ||
						strings.Contains(categoryType, categoryNameLower) {
						return category.ID
					}
				}
			}
		}
	}

	// If no specific match found, return the first category of the correct type
	for _, category := range categories {
		return category.ID // Return the first available category
	}

	// Final fallback to hardcoded matching
	return utils.GetCategoryIDForTransaction(description, transactionType)
}

// getAccountIDForTransaction finds the best matching account ID from the user's accounts
func (s *service) getAccountIDForTransaction(userID, description string) string {
	// Get all accounts for the user
	accounts, err := s.accountService.GetAllAccounts(userID)
	if err != nil {
		fmt.Printf("Error getting accounts: %v\n", err)
		// Fallback to hardcoded account matching
		return utils.GetAccountIDForTransaction(description)
	}

	// If user has accounts, return the first one (default account)
	if len(accounts) > 0 {
		return accounts[0].ID
	}

	// Final fallback to hardcoded matching
	return utils.GetAccountIDForTransaction(description)
}

// ParseVakifbankStatement parses a Vakifbank statement PDF and extracts transactions
func (s *service) ParseVakifbankStatement(userID string, file multipart.File) ([]dtos.BankStatementEntry, error) {
	var buf bytes.Buffer
	_, err := io.Copy(&buf, file)
	if err != nil {
		return nil, fmt.Errorf("pdf dosyası okunamadı: %w", err)
	}

	// PDF dökümanını aç
	pdfReader, err := pdf.NewReader(bytes.NewReader(buf.Bytes()), int64(buf.Len()))
	if err != nil {
		return nil, fmt.Errorf("pdf okunamadı: %w", err)
	}

	// Tüm sayfalardaki metni birleştir
	var allText string
	numPages := pdfReader.NumPage()
	for i := 1; i <= numPages; i++ {
		page := pdfReader.Page(i)
		if page.V.IsNull() {
			continue
		}

		// Sayfadaki tüm içerikleri dolaş
		content := page.Content()
		if content.Text == nil || len(content.Text) == 0 {
			continue
		}

		for _, text := range content.Text {
			allText += text.S
		}
	}

	// PDF içeriğini alıp işleme fonksiyonuna gönder
	return s.parseVakifbankStatementText(userID, allText)
}

// parseVakifbankStatementText parses the extracted text from a Vakifbank statement
func (s *service) parseVakifbankStatementText(userID, text string) ([]dtos.BankStatementEntry, error) {
	var entries []dtos.BankStatementEntry

	scanner := bufio.NewScanner(strings.NewReader(text))
	var lines []string
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" {
			lines = append(lines, line)
		}
	}

	dateRegex := regexp.MustCompile(`^\d{2}\.\d{2}\.\d{4}$`)
	// timeRegex := regexp.MustCompile(`^\d{2}:\d{2}$`)
	// amountRegex := regexp.MustCompile(`^-?[\d.]+,\d{2}$`)

	i := 0
	for i < len(lines) {
		line := lines[i]
		if dateRegex.MatchString(line) && i+4 < len(lines) {
			date := lines[i]
			timeVal := lines[i+1]
			amountStr := lines[i+3]
			descriptionLines := []string{}
			explanation := ""

			// Miktarı parse et
			amountClean := strings.ReplaceAll(amountStr, ".", "")
			amountClean = strings.ReplaceAll(amountClean, ",", ".")
			amountFloat, err := strconv.ParseFloat(amountClean, 64)
			if err != nil {
				i++
				continue
			}

			// Açıklamaları topla
			j := i + 5
			for j < len(lines) {
				if dateRegex.MatchString(lines[j]) {
					break
				}
				descriptionLines = append(descriptionLines, lines[j])
				j++
			}

			// Açıklamanın içinde parantezli detay varsa ayır
			fullDescription := strings.Join(descriptionLines, " ")
			fullDescription = strings.ReplaceAll(fullDescription, "�", " ") // garip karakter temizliği

			if start := strings.Index(fullDescription, "("); start != -1 {
				if end := strings.Index(fullDescription, ")"); end > start {
					explanation = fullDescription[start : end+1]
					fullDescription = strings.TrimSpace(fullDescription[:start])
				}
			}

			transactionType := "expense"
			if amountFloat > 0 {
				transactionType = "income"
			}

			categoryID := s.getCategoryIDForTransaction(userID, fullDescription, transactionType)
			accountID := s.getAccountIDForTransaction(userID, fullDescription)

			entry := dtos.BankStatementEntry{
				Date:        fmt.Sprintf("%s %s", date, timeVal),
				Description: strings.TrimSpace(fullDescription + " " + explanation),
				Amount:      math.Abs(amountFloat),
				Type:        transactionType,
				CategoryID:  categoryID,
				AccountID:   accountID,
			}
			entries = append(entries, entry)
			i = j
		} else {
			i++
		}
	}

	fmt.Printf("Found %d transaction entries\n", len(entries))
	return entries, nil
}

// ImportBankStatementEntries imports bank statement entries as transactions
func (s *service) ImportBankStatementEntries(userID string, entries []dtos.BankStatementImportRequest) ([]dtos.TransactionResponse, error) {
	var responses []dtos.TransactionResponse

	for _, entry := range entries {
		// Convert date format if needed
		var transactionDate time.Time
		var err error

		// Try different date formats
		dateFormats := []string{
			"02.01.2006 15:04",    // DD.MM.YYYY HH:MM (from PDF parsing)
			"02.01.2006",          // DD.MM.YYYY
			"2006-01-02",          // YYYY-MM-DD
			"2006-01-02 15:04:05", // YYYY-MM-DD HH:MM:SS
		}

		for _, format := range dateFormats {
			transactionDate, err = time.Parse(format, entry.Date)
			if err == nil {
				break
			}
		}

		if err != nil {
			return nil, fmt.Errorf("invalid date format: %s", entry.Date)
		}

		// Auto-assign category if not provided or empty
		categoryID := entry.CategoryID
		if categoryID == "" {
			categoryID = s.getCategoryIDForTransaction(userID, entry.Description, entry.Type)
		}

		// Auto-assign account if not provided or empty
		accountID := entry.AccountID
		if accountID == "" {
			accountID = s.getAccountIDForTransaction(userID, entry.Description)
		}

		// Create transaction request
		transactionReq := &dtos.TransactionRequest{
			Title:           entry.Description,
			Type:            entry.Type,
			Amount:          entry.Amount,
			Currency:        "TRY",           // Default to Turkish Lira
			CategoryID:      categoryID,      // Auto-assigned if needed
			PaymentMethod:   "bank_transfer", // Default payment method
			AccountID:       accountID,       // Auto-assigned if needed
			TransactionDate: transactionDate,
		}

		// Create transaction
		response, err := s.transactionService.CreateTransaction(userID, transactionReq)
		if err != nil {
			return nil, err
		}

		responses = append(responses, *response)
	}

	return responses, nil
}
